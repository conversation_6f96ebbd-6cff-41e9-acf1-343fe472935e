import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { PerfectScrollbarModule } from 'ngx-perfect-scrollbar';
import { PERFECT_SCROLLBAR_CONFIG } from 'ngx-perfect-scrollbar';
import { PerfectScrollbarConfigInterface } from 'ngx-perfect-scrollbar';

import {
  NgbNavModule,
  NgbDropdownModule,
  NgbModalModule,
  NgbTooltipModule,
} from '@ng-bootstrap/ng-bootstrap';
import { NgApexchartsModule } from 'ng-apexcharts';

import { WidgetModule } from '../shared/widget/widget.module';
import { UIModule } from '../shared/ui/ui.module';

import { FullCalendarModule } from '@fullcalendar/angular';

import { PagesRoutingModule } from './pages-routing.module';

import { DashboardsModule } from './dashboards/dashboards.module';
import { TeamModule } from './team/team.module';
import { ReportsModule } from './reports/reports.module';
import { RaceModule } from './race/race.module';
import { EvaluationModule } from './evaluation/evaluation.module';
import { AppointmentModule } from './appointment/appointment.module';
import { SettingsModule } from './settings/settings.module';
import { AdminModule } from './admin/admin.module';
import { TechnicalDesignModule } from './technical-design/technical-design.module';
import { StatisticsModule } from './statistics/statistics.module';
import { HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http';
import { PetitionModule } from './petition/petition.module';
import { LoaderService } from '../core/services/loader.service';
import { LoaderInterceptorService } from '../core/services/interceptors/loader-interceptor.service';
import { InspectionDetailModule } from './inspection-detail/inspection-detail.module';
import { AccelerationResultsModule } from './acceleration-results/acceleration-results.module';
import { ReportsDetailComponent } from './reports-detail/reports-detail.component';
import {NgxDatatableModule} from '@swimlane/ngx-datatable';
import {TableModule} from 'primeng/table';
const DEFAULT_PERFECT_SCROLLBAR_CONFIG: PerfectScrollbarConfigInterface = {
  suppressScrollX: true,
  wheelSpeed: 0.3
};

@NgModule({
  declarations: [ReportsDetailComponent],
  imports: [
    CommonModule,
    FormsModule,
    NgbDropdownModule,
    NgbModalModule,
    PagesRoutingModule,
    NgApexchartsModule,
    ReactiveFormsModule,
    DashboardsModule,
    TeamModule,
    ReportsModule,
    RaceModule,
    EvaluationModule,
    AppointmentModule,
    SettingsModule,
    AdminModule,
    TechnicalDesignModule,
    HttpClientModule,
    UIModule,
    WidgetModule,
    FullCalendarModule,
    NgbNavModule,
    NgbTooltipModule,
    PerfectScrollbarModule,
    StatisticsModule,
    PetitionModule,
    AccelerationResultsModule,
    NgxDatatableModule,
    TableModule
  ],
  providers: [
    {
      provide: PERFECT_SCROLLBAR_CONFIG,
      useValue: DEFAULT_PERFECT_SCROLLBAR_CONFIG
    },
    LoaderService,
    { provide: HTTP_INTERCEPTORS, useClass: LoaderInterceptorService, multi: true }
  ]
})
export class PagesModule { }
