import { Component, Input, OnInit, OnChanges } from '@angular/core';

interface TournamentTeam {
  rank: number;
  logo: string;
  teamName: string;
  vehicleNo: number;
  universityName: string;
  time: string;
}

interface BracketMatch {
  id: string;
  team1?: TournamentTeam;
  team2?: TournamentTeam;
  winner?: TournamentTeam;
  round: number;
  matchNumber: number;
}

@Component({
  selector: 'app-tournament-bracket',
  templateUrl: './tournament-bracket.component.html',
  styleUrls: ['./tournament-bracket.component.scss']
})
export class TournamentBracketComponent implements OnInit, OnChanges {
  @Input() teams: TournamentTeam[] = [];

  rounds: BracketMatch[][] = [];
  roundNames: string[] = [];

  constructor() { }

  ngOnInit(): void {
    this.generateBracket();
  }

  ngOnChanges(): void {
    if (this.teams.length > 0) {
      this.generateBracket();
    }
  }

  generateBracket(): void {
    if (this.teams.length === 0) return;

    // Filter out teams with null time (DNF teams)
    const qualifiedTeams = this.teams.filter(team => team.time !== null);

    // Ensure we have a power of 2 number of teams for proper bracket
    const bracketSize = this.getNextPowerOfTwo(qualifiedTeams.length);
    const teamsToUse = qualifiedTeams.slice(0, bracketSize);

    this.rounds = [];
    this.generateRoundNames(bracketSize);

    // Generate first round
    const firstRound = this.generateFirstRound(teamsToUse);
    this.rounds.push(firstRound);

    // Generate subsequent rounds
    let currentRound = firstRound;
    let roundNumber = 2;

    while (currentRound.length > 1) {
      const nextRound = this.generateNextRound(currentRound, roundNumber);
      this.rounds.push(nextRound);
      currentRound = nextRound;
      roundNumber++;
    }
  }

  private getNextPowerOfTwo(n: number): number {
    if (n <= 1) return 2;
    if (n <= 2) return 2;
    if (n <= 4) return 4;
    if (n <= 8) return 8;
    if (n <= 16) return 16;
    return 32;
  }

  private generateRoundNames(bracketSize: number): void {
    this.roundNames = [];
    let rounds = Math.log2(bracketSize);

    for (let i = 1; i <= rounds; i++) {
      if (i === rounds) {
        this.roundNames.push('Final');
      } else if (i === rounds - 1) {
        this.roundNames.push('Semi-Final');
      } else if (i === rounds - 2) {
        this.roundNames.push('Quarter-Final');
      } else {
        this.roundNames.push(`Round ${i}`);
      }
    }
  }

  private generateFirstRound(teams: TournamentTeam[]): BracketMatch[] {
    const matches: BracketMatch[] = [];

    for (let i = 0; i < teams.length; i += 2) {
      const team1 = teams[i];
      const team2 = teams[i + 1] || null;

      const match: BracketMatch = {
        id: `round1-match${i/2 + 1}`,
        team1: team1,
        team2: team2,
        round: 1,
        matchNumber: i/2 + 1
      };

      // Determine winner automatically
      match.winner = this.getMatchWinner(team1, team2);

      matches.push(match);
    }

    return matches;
  }

  private generateNextRound(previousRound: BracketMatch[], roundNumber: number): BracketMatch[] {
    const matches: BracketMatch[] = [];

    for (let i = 0; i < previousRound.length; i += 2) {
      const team1 = previousRound[i]?.winner;
      const team2 = previousRound[i + 1]?.winner;

      const match: BracketMatch = {
        id: `round${roundNumber}-match${i/2 + 1}`,
        team1: team1 || null,
        team2: team2 || null,
        round: roundNumber,
        matchNumber: i/2 + 1
      };

      // Determine winner for this round
      match.winner = this.getMatchWinner(team1, team2);

      matches.push(match);
    }

    return matches;
  }

  getTeamDisplayName(team: TournamentTeam): string {
    return `#${team.vehicleNo} ${team.teamName}`;
  }

  getTeamShortName(team: TournamentTeam): string {
    return team.teamName.length > 15 ? team.teamName.substring(0, 15) + '...' : team.teamName;
  }

  isWinner(match: BracketMatch, team: TournamentTeam): boolean {
    return match.winner && team && match.winner.vehicleNo === team.vehicleNo;
  }

  getMatchWinner(team1: TournamentTeam, team2: TournamentTeam): TournamentTeam | null {
    if (!team1 && !team2) return null;
    if (!team1) return team2;
    if (!team2) return team1;

    // Winner is determined by better (lower) time
    // Handle null times (DNF) - team with valid time wins
    if (team1.time === null && team2.time !== null) return team2;
    if (team2.time === null && team1.time !== null) return team1;
    if (team1.time === null && team2.time === null) return team1; // Default to team1 if both DNF

    // Compare times (assuming time is in string format like "12.345")
    const time1 = parseFloat(team1.time);
    const time2 = parseFloat(team2.time);

    return time1 <= time2 ? team1 : team2;
  }
}
