// Import application variables
@import '../../../../assets/scss/variables';

.tournament-container {
  margin-bottom: $grid-gutter-width;
  padding: $card-spacer-y $card-spacer-x;
  background: $card-bg;
  border-radius: $card-border-radius;
  box-shadow: $box-shadow;
  border: $card-border-width solid $card-border-color;

  @media (max-width: 768px) {
    padding: $card-spacer-y / 2 $card-spacer-x / 2;
  }

  .tournament-header {
    margin-bottom: 2rem;
    text-align: center;

    h3 {
      color: $gray-800;
      font-weight: $font-weight-semibold;
      margin-bottom: 0.5rem;
      font-size: 1.5rem;
    }

    p {
      color: $gray-600;
      margin-bottom: 0;
      font-size: 0.9rem;
    }
  }
}

.bracket {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  gap: 3rem;
  overflow-x: auto;
  padding: 1rem 0;
  min-height: 400px;

  @media (max-width: 768px) {
    gap: 2rem;
    padding: 0.5rem 0;
  }

  .round {
    display: flex;
    flex-direction: column;
    align-items: center;
    min-width: 200px;

    .round-title {
      margin-bottom: 1.5rem;
      text-align: center;

      h4 {
        color: $primary;
        font-weight: $font-weight-semibold;
        font-size: 1.1rem;
        margin: 0;
        padding: 0.5rem 1rem;
        background: lighten($primary, 45%);
        border-radius: $border-radius;
        border: 1px solid lighten($primary, 35%);
      }
    }

    .winners {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 100%;

      .matchups {
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
        width: 100%;

        .matchup {
          .participants {
            border-radius: $border-radius;
            overflow: hidden;
            box-shadow: $box-shadow-sm;
            border: 1px solid $border-color;

            .participant {
              background: $card-bg;
              border-left: 4px solid $gray-400;
              padding: 0.75rem;
              transition: $transition-base;
              min-height: 70px;
              display: flex;
              align-items: center;

              &:not(:last-child) {
                border-bottom: 1px solid $border-color;
              }

              &.winner {
                border-left-color: $success;
                background: lighten($success, 50%);

                .team-name {
                  font-weight: $font-weight-semibold;
                  color: darken($success, 20%);
                }
              }

              &.bye {
                background: $gray-100;
                border-left-color: $gray-300;
                opacity: 0.7;

                .team-name {
                  font-style: italic;
                  color: $gray-500;
                }
              }

              .team-info {
                display: flex;
                align-items: center;
                gap: 0.75rem;
                width: 100%;

                .team-logo {
                  flex-shrink: 0;
                  width: 32px;
                  height: 32px;
                  border-radius: $border-radius-sm;
                  overflow: hidden;
                  background: $gray-100;
                  display: flex;
                  align-items: center;
                  justify-content: center;

                  img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                  }
                }

                .team-details {
                  flex: 1;
                  min-width: 0;

                  .team-name {
                    font-size: 0.9rem;
                    font-weight: $font-weight-medium;
                    color: $gray-800;
                    margin-bottom: 0.25rem;
                    line-height: 1.2;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                  }

                  .vehicle-number {
                    font-size: 0.75rem;
                    color: $primary;
                    font-weight: $font-weight-semibold;
                    margin-bottom: 0.25rem;
                  }

                  .team-time {
                    font-size: 0.7rem;
                    color: $gray-600;
                    font-family: monospace;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
// Connector lines between rounds
.bracket .round .winners .connector {
  margin: 2rem 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;

  .line, .merger {
    border-color: $border-color;
  }

  .line {
    width: 2rem;
    height: 2px;
    background: $border-color;
    border-radius: 1px;
  }

  .merger {
    position: relative;
    width: 2rem;
    height: 4rem;

    &:before, &:after {
      content: "";
      position: absolute;
      width: 50%;
      height: 50%;
      border: 1px solid $border-color;
      border-left: none;
    }

    &:before {
      top: 0;
      right: 0;
      border-bottom: none;
      border-top-right-radius: $border-radius-sm;
    }

    &:after {
      bottom: 0;
      right: 0;
      border-top: none;
      border-bottom-right-radius: $border-radius-sm;
    }
  }

  &.filled {
    .line, .merger:before, .merger:after {
      border-color: $success;
    }
  }
}

// Round-specific spacing adjustments
.bracket .round {
  &.quarterfinals .winners .matchups .matchup:not(:last-child) {
    margin-bottom: 1.5rem;
  }

  &.semifinals .winners .matchups .matchup:not(:last-child) {
    margin-bottom: 4rem;
  }

  &.finals .winners .matchups .matchup {
    margin-bottom: 0;
  }
}

// Empty state styling
.empty-bracket {
  text-align: center;
  padding: 4rem 2rem;
  color: $gray-600;

  .empty-message {
    i {
      font-size: 4rem;
      color: $gray-400;
      margin-bottom: 1rem;
      display: block;
    }

    h4 {
      color: $gray-700;
      font-weight: $font-weight-medium;
      margin-bottom: 0.5rem;
    }

    p {
      color: $gray-600;
      margin: 0;
      font-size: 0.9rem;
    }
  }
}

// Responsive adjustments
@media (max-width: 992px) {
  .bracket {
    gap: 2rem;

    .round .round-title h4 {
      font-size: 1rem;
      padding: 0.4rem 0.8rem;
    }

    .round .winners .matchups .participant {
      padding: 0.6rem;
      min-height: 60px;

      .team-info {
        gap: 0.5rem;

        .team-logo {
          width: 28px;
          height: 28px;
        }

        .team-details {
          .team-name {
            font-size: 0.85rem;
          }

          .vehicle-number {
            font-size: 0.7rem;
          }

          .team-time {
            font-size: 0.65rem;
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .bracket {
    gap: 1.5rem;
    flex-direction: column;
    align-items: center;

    .round {
      min-width: 180px;
      width: 100%;
      max-width: 300px;

      .round-title h4 {
        font-size: 0.95rem;
      }

      .winners .matchups .participant {
        padding: 0.5rem;
        min-height: 55px;

        .team-info .team-details .team-name {
          font-size: 0.8rem;
        }
      }
    }
  }

  .empty-bracket {
    padding: 3rem 1rem;

    .empty-message i {
      font-size: 3rem;
    }
  }
}
