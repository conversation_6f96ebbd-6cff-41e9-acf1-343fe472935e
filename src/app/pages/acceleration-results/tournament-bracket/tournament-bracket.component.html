<div class="tournament-container">
  <div class="tournament-header">
    <h3>Tournament Bracket</h3>
    <p>Elimination-style tournament based on acceleration results</p>
  </div>

  <div class="bracket" *ngIf="rounds.length > 0">
    <section
      *ngFor="let round of rounds; let roundIndex = index"
      class="round"
      [ngClass]="{
        'quarterfinals': roundNames[roundIndex]?.includes('Quarter'),
        'semifinals': roundNames[roundIndex]?.includes('Semi'),
        'finals': roundNames[roundIndex]?.includes('Final')
      }">

      <div class="round-title">
        <h4>{{ roundNames[roundIndex] }}</h4>
      </div>

      <div class="winners">
        <div class="matchups">
          <div
            *ngFor="let match of round"
            class="matchup">
            <div class="participants">
              <!-- Team 1 -->
              <div
                class="participant"
                [ngClass]="{ 'winner': match.winner && match.team1 && match.winner.vehicleNo === match.team1.vehicleNo }"
                *ngIf="match.team1">
                <div class="team-info">
                  <div class="team-logo" *ngIf="match.team1.logo">
                    <img [src]="match.team1.logo" [alt]="match.team1.teamName + ' logo'">
                  </div>
                  <div class="team-details">
                    <div class="team-name">{{ getTeamShortName(match.team1) }}</div>
                    <div class="vehicle-number">#{{ match.team1.vehicleNo }}</div>
                    <div class="team-time" *ngIf="match.team1.time">{{ match.team1.time }}</div>
                  </div>
                </div>
              </div>

              <!-- Team 2 -->
              <div
                class="participant"
                [ngClass]="{ 'winner': match.winner && match.team2 && match.winner.vehicleNo === match.team2.vehicleNo }"
                *ngIf="match.team2">
                <div class="team-info">
                  <div class="team-logo" *ngIf="match.team2.logo">
                    <img [src]="match.team2.logo" [alt]="match.team2.teamName + ' logo'">
                  </div>
                  <div class="team-details">
                    <div class="team-name">{{ getTeamShortName(match.team2) }}</div>
                    <div class="vehicle-number">#{{ match.team2.vehicleNo }}</div>
                    <div class="team-time" *ngIf="match.team2.time">{{ match.team2.time }}</div>
                  </div>
                </div>
              </div>

              <!-- Bye placeholder -->
              <div class="participant bye" *ngIf="!match.team2 && match.team1">
                <div class="team-info">
                  <div class="team-details">
                    <div class="team-name">BYE</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Connector lines for non-final rounds -->
        <div class="connector" *ngIf="roundIndex < rounds.length - 1">
          <div class="merger"></div>
          <div class="line"></div>
        </div>
      </div>
    </section>
  </div>

  <!-- Empty state -->
  <div class="empty-bracket" *ngIf="rounds.length === 0">
    <div class="empty-message">
      <i class="mdi mdi-tournament"></i>
      <h4>No Tournament Data</h4>
      <p>Tournament bracket will be generated when team data is available.</p>
    </div>
  </div>
</div>
